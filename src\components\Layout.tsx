import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface LayoutProps {
  children: ReactNode;
  className?: string;
}

const Layout = ({ children, className }: LayoutProps) => {
  return (
    <div
      className={cn(
        'min-h-screen flex flex-col',
        className
      )}
    >
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8">
        <div className="w-full max-w-4xl">
          <div
            className="relative overflow-hidden rounded-2xl"
            style={{
              background: 'var(--glass-bg)',
              border: '1px solid var(--glass-border)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)'
            }}
          >
            <div className="relative z-10 p-6 sm:p-8 lg:p-12">
              {children}
            </div>
            {/* Glass panel decorative overlay */}
            <div 
              className="absolute inset-0 opacity-20 pointer-events-none"
              style={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.05) 100%)'
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout;
