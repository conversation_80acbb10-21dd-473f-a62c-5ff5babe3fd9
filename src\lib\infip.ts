export interface GenerateParams {
  prompt: string;
  num_images?: number;
  size?: '1024x1024';
}

export interface GenerateResponse {
  images: string[];
  created: number;
  usage: null;
}

/**
 * Generates images based on a prompt.
 * @param params The generation parameters.
 * @returns The generated images.
 */
export async function generateImages({
  prompt,
  num_images,
  size
}: GenerateParams): Promise<GenerateResponse> {
  const response = await fetch('http://localhost:3200/api/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      prompt,
      n: num_images,
      size
    }),
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data as GenerateResponse;
}
