@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './themes.css';

@layer base {
  :root {
    /* Dark glassmorphism theme colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 0 100% 50%; /* Neon cyan */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 288 96% 67%; /* Neon purple */
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 180 100% 50%; /* Neon cyan */
    --radius: 0.75rem;
    
    /* Glassmorphism backdrop */
    --glass-bg: rgba(17, 17, 17, 0.75);
    --glass-border: rgba(255, 255, 255, 0.1);
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 0 100% 50%; /* Neon cyan */
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 288 96% 67%; /* Neon purple */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 180 100% 50%; /* Neon cyan */
    
    /* Light glassmorphism backdrop */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.2);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    @apply scroll-smooth h-full;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased h-full;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
    background-attachment: fixed;
  }

  #root {
    @apply h-full;
  }
}

@layer components {
  /* Custom glassmorphism button */
  .btn-glass {
    @apply glass-panel glass-panel-hover px-6 py-3 font-medium transition-all duration-300 
           hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-ring;
  }
  
  /* Custom neon input */
  .input-neon {
    @apply glass-panel px-4 py-2 text-foreground placeholder-muted-foreground 
           focus:ring-2 focus:ring-neon-cyan focus:border-neon-cyan transition-all duration-300;
  }
  
  /* Custom glass card */
  .card-glass {
    @apply glass-panel p-6 animate-fade-in-up;
  }
  
  /* Custom backdrop overlay */
  .backdrop-glass {
    @apply fixed inset-0 bg-backdrop-dark backdrop-blur-md animate-backdrop-blur-in;
  }
  
  .light .backdrop-glass {
    @apply bg-backdrop-light;
  }
}

@layer utilities {
  /* Custom backdrop filter utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  .backdrop-blur-light {
    backdrop-filter: blur(8px);
  }
  
  .backdrop-blur-heavy {
    backdrop-filter: blur(20px);
  }
  
  /* Text shadow utilities for neon effect */
  .text-shadow-neon {
    text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 40px currentColor;
  }
  
  .text-shadow-glow {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }
  
  /* Custom scrollbar */
  .scrollbar-glass::-webkit-scrollbar {
    width: 8px;
  }
  
  .scrollbar-glass::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  .scrollbar-glass::-webkit-scrollbar-thumb {
    @apply bg-glass-border rounded-full;
  }
  
  .scrollbar-glass::-webkit-scrollbar-thumb:hover {
    @apply bg-glass-border-dark;
  }

  /* Image showcase utilities */
  .image-showcase-main {
    @apply transition-all duration-500 ease-out;
  }

  .image-showcase-main:hover {
    transform: scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* Image showcase layout */
  .image-showcase-container {
    max-width: 600px;
    margin: 0 auto;
  }

  .thumbnail-row {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  @media (max-width: 640px) {
    .thumbnail-row {
      gap: 0.5rem;
    }
  }

  .thumbnail-item {
    @apply transition-all duration-300 ease-out;
  }

  .thumbnail-item:hover {
    transform: translateY(-2px) scale(1.05);
  }

  .thumbnail-active {
    transform: translateY(-4px) scale(1.1);
    box-shadow: 0 10px 25px -5px rgba(6, 182, 212, 0.4), 0 0 0 2px rgba(6, 182, 212, 0.5);
  }
}
