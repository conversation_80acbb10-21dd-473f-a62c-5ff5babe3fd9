import * as React from "react"
import { cn } from "@/lib/utils"
import { Loader2 } from "lucide-react"

// Spinner component
interface SpinnerProps {
  size?: "sm" | "md" | "lg"
  className?: string
}

export const Spinner: React.FC<SpinnerProps> = ({ size = "md", className }) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8"
  }
  
  return (
    <Loader2 className={cn("animate-spin", sizeClasses[size], className)} />
  )
}

// ErrorAlert component
interface ErrorAlertProps {
  children: React.ReactNode
  variant?: "default" | "destructive"
  className?: string
}

export const ErrorAlert: React.FC<ErrorAlertProps> = ({ 
  children, 
  variant = "default",
  className 
}) => {
  const variantClasses = {
    default: "border-border text-foreground",
    destructive: "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"
  }
  
  return (
    <div className={cn(
      "relative w-full rounded-lg border p-4",
      variantClasses[variant],
      className
    )}>
      {children}
    </div>
  )
}

// Re-export all components
export { Button } from "./Button"
export { Input } from "./Input" 
export { Label } from "./Label"
export { Textarea } from "./Textarea"
export {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./Select"
