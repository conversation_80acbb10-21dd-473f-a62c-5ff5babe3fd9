import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/Button';
import { Textarea } from '@/components/ui/Textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { Label } from '@/components/ui/Label';
import { Wand2, ImageIcon, Download, Copy, Share2, RefreshCw, Sparkles, Zap, Palette, Heart, X, ZoomIn } from 'lucide-react';
import { generateImages } from '@/lib/infip';

const PROMPT_SUGGESTIONS = [
  "A majestic dragon soaring through clouds at sunset, fantasy art style",
  "Cyberpunk cityscape with neon lights reflecting on wet streets",
  "Serene mountain lake with crystal clear water and pine trees",
  "Steampunk airship floating above Victorian-era London",
  "Abstract geometric patterns in vibrant colors, digital art"
];

const ImageGenerator: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [size, setSize] = useState<'1024x1024'>('1024x1024');
  const [numImages, setNumImages] = useState(4);
  const [displayedImages, setDisplayedImages] = useState<string[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [mainImageIndex, setMainImageIndex] = useState(0);
  const [imageLoadingStates, setImageLoadingStates] = useState<{[key: string]: boolean}>({});
  const [mainImageLoading, setMainImageLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: generateImages,
    onSuccess: (data) => {
      setDisplayedImages(data.images);
      setMainImageIndex(0);
      setMainImageLoading(false);
      setImageLoadingStates({});
    },
    onError: (error) => {
      console.error('Generation failed:', error);
      setMainImageLoading(false);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim()) return;
    
    mutation.mutate({
      prompt: prompt.trim(),
      size,
      num_images: numImages,
    });
  };

  const handleRandomPrompt = () => {
    const randomPrompt = PROMPT_SUGGESTIONS[Math.floor(Math.random() * PROMPT_SUGGESTIONS.length)];
    setPrompt(randomPrompt);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setPrompt(suggestion);
  };

  const downloadImage = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const copyImageUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
    } catch (error) {
      console.error('Copy failed:', error);
    }
  };

  const openImageModal = (url: string, index: number) => {
    setSelectedImage(url);
    setSelectedImageIndex(index);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
    setSelectedImageIndex(null);
  };

  const handleImageLoad = (url: string) => {
    setImageLoadingStates(prev => ({ ...prev, [url]: false }));
  };

  const handleImageLoadStart = (url: string) => {
    setImageLoadingStates(prev => ({ ...prev, [url]: true }));
  };

  const handleMainImageChange = (index: number) => {
    setMainImageLoading(true);
    setMainImageIndex(index);
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 text-white">
      {/* Mobile Header */}
      <div className="lg:hidden bg-slate-800/50 backdrop-blur-xl border-b border-slate-700/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center">
              <Wand2 className="w-4 h-4 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">AI Studio</h1>
              <p className="text-xs text-slate-400">Image Generator</p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row min-h-screen lg:h-screen">
        {/* Left Sidebar */}
        <div className="w-full lg:w-80 xl:w-96 bg-slate-800/50 backdrop-blur-xl border-r border-slate-700/50 flex flex-col shadow-2xl order-2 lg:order-1">
          {/* Desktop App Header */}
          <div className="hidden lg:block p-4 border-b border-slate-700/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <Wand2 className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-white">AI Studio</h1>
                  <p className="text-xs text-slate-400">Image Generator</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-400 hover:text-white p-2 rounded-lg hover:bg-slate-700/50"
                >
                  <Heart className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Control Header */}
          <div className="p-4 lg:p-6 border-b border-slate-700/50">
            <div className="flex items-center gap-3 mb-4 lg:mb-6">
              <div className="w-8 lg:w-10 h-8 lg:h-10 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                <Wand2 className="w-4 lg:w-5 h-4 lg:h-5 text-white" />
              </div>
              <h2 className="text-lg lg:text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                Describe your vision
              </h2>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-6">
              {/* Prompt Input */}
              <div className="relative">
                <Textarea
                  placeholder="A futuristic cityscape at sunset, cinematic lighting, hyper-realistic, 8K resolution..."
                  className="min-h-[100px] lg:min-h-[120px] bg-slate-700/50 border-slate-600/50 text-white placeholder-slate-400 resize-none rounded-xl backdrop-blur-sm focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50 transition-all duration-200 text-sm lg:text-base"
                  value={prompt}
                  onChange={e => setPrompt(e.target.value)}
                  disabled={mutation.isPending}
                  required
                />
                <div className="absolute bottom-3 right-3 flex gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRandomPrompt}
                    className="h-8 w-8 p-0 text-slate-400 hover:text-cyan-400 hover:bg-slate-600/50 rounded-lg"
                    disabled={mutation.isPending}
                  >
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Number of Images Input */}
              <div className="space-y-2">
                <Label htmlFor="numImages" className="text-sm font-medium text-slate-200">
                  Number of Images
                </Label>
                <Select
                  value={numImages.toString()}
                  onValueChange={(value) => setNumImages(parseInt(value))}
                  disabled={mutation.isPending}
                >
                  <SelectTrigger className="bg-slate-700/50 border-slate-600/50 text-white rounded-xl backdrop-blur-sm focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50 transition-all duration-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-600 text-white">
                    <SelectItem value="1">1 Image</SelectItem>
                    <SelectItem value="2">2 Images</SelectItem>
                    <SelectItem value="3">3 Images</SelectItem>
                    <SelectItem value="4">4 Images</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Generate Button */}
              <Button
                type="submit"
                disabled={mutation.isPending || !prompt.trim()}
                className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {mutation.isPending ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Generating...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Wand2 className="w-4 h-4" />
                    Generate Images
                  </div>
                )}
              </Button>
            </form>
          </div>

          {/* Prompt Suggestions */}
          <div className="flex-1 p-4 lg:p-6 overflow-y-auto scrollbar-glass">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 bg-gradient-to-r from-violet-500 to-purple-500 rounded-md flex items-center justify-center">
                    <Sparkles className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-xs lg:text-sm font-medium text-slate-200">Need inspiration? Try these prompts</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRandomPrompt}
                  className="text-slate-400 hover:text-cyan-400 p-2 rounded-lg hover:bg-slate-700/50 transition-colors"
                  disabled={mutation.isPending}
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="space-y-2 lg:space-y-3">
                {PROMPT_SUGGESTIONS.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left p-3 lg:p-4 rounded-xl bg-slate-700/30 hover:bg-slate-600/50 border border-slate-600/30 hover:border-slate-500/50 transition-all duration-200 text-xs lg:text-sm text-slate-300 hover:text-white backdrop-blur-sm group"
                    disabled={mutation.isPending}
                  >
                    <div className="flex items-start gap-2 lg:gap-3">
                      <div className="w-5 lg:w-6 h-5 lg:h-6 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform">
                        <Sparkles className="w-2.5 lg:w-3 h-2.5 lg:h-3 text-white" />
                      </div>
                      <span className="leading-relaxed">{suggestion}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col relative overflow-hidden order-1 lg:order-2">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10"></div>
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.1) 0%, transparent 50%), 
                               radial-gradient(circle at 75% 75%, rgba(255, 119, 198, 0.1) 0%, transparent 50%)`
            }}></div>
          </div>

          {/* Main Display Area */}
          <div className="flex-1 flex items-center justify-center p-4 lg:p-8 relative z-10">
            {/* Main Container */}
            <div className="w-full max-w-6xl mx-auto p-6 lg:p-8 min-h-[400px] lg:min-h-[500px] flex items-center justify-center">
              {mutation.isPending ? (
                <div className="text-center">
                  <div className="relative">
                    <div className="w-16 lg:w-20 h-16 lg:h-20 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 lg:mb-6 animate-pulse">
                      <Wand2 className="w-8 lg:w-10 h-8 lg:h-10 text-white" />
                    </div>
                    <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full opacity-20 animate-ping"></div>
                  </div>
                  <h3 className="text-lg lg:text-xl font-semibold mb-2 text-slate-200">Creating your masterpiece...</h3>
                  <p className="text-slate-400 text-sm lg:text-base">This may take a few moments</p>
                </div>
              ) : displayedImages && displayedImages.length > 0 ? (
                <div className="w-full image-showcase-container space-y-4">
                  {/* Main Image Display */}
                  <div className="relative group">
                    <div className="relative overflow-hidden rounded-2xl bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 shadow-2xl image-showcase-main">
                      <div className="aspect-square overflow-hidden relative">
                        {/* Loading overlay */}
                        {mainImageLoading && (
                          <div className="absolute inset-0 bg-slate-800/80 backdrop-blur-sm flex items-center justify-center z-10">
                            <div className="flex flex-col items-center gap-3">
                              <div className="w-8 h-8 border-2 border-cyan-500/30 border-t-cyan-500 rounded-full animate-spin"></div>
                              <p className="text-slate-300 text-sm">Loading image...</p>
                            </div>
                          </div>
                        )}
                        <img
                          src={displayedImages[mainImageIndex]}
                          alt={`Generated ${mainImageIndex + 1}`}
                          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                          loading="lazy"
                          onClick={() => openImageModal(displayedImages[mainImageIndex], mainImageIndex)}
                          onLoad={() => setMainImageLoading(false)}
                          onLoadStart={() => setMainImageLoading(true)}
                        />
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>



                      {/* Click to expand overlay */}
                      <div
                        className="absolute inset-0 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        onClick={() => openImageModal(displayedImages[mainImageIndex], mainImageIndex)}
                      >
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="bg-black/60 backdrop-blur-sm rounded-full p-3 transform scale-75 group-hover:scale-100 transition-transform duration-300">
                            <ZoomIn className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </div>

                      {/* Image Counter */}
                      <div className="absolute bottom-4 left-4">
                        <div className="bg-black/80 backdrop-blur-sm rounded-xl px-3 py-1 shadow-lg">
                          <p className="text-white text-sm font-medium">{mainImageIndex + 1} / {displayedImages.length}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Thumbnail Gallery - Show remaining images as thumbnails */}
                  {displayedImages.length > 1 && (
                    <div className="space-y-2">

                      {/* Horizontal thumbnail row */}
                      <div className="thumbnail-row">
                        {displayedImages.slice(0, 4).map((url, i) => (
                          <div
                            key={url + ':' + i}
                            className={`relative group cursor-pointer transition-all duration-300 ${
                              i === mainImageIndex
                                ? 'ring-2 ring-cyan-500 ring-offset-2 ring-offset-slate-900 scale-105 shadow-lg shadow-cyan-500/25'
                                : 'hover:scale-105 hover:ring-2 hover:ring-slate-400 hover:ring-offset-2 hover:ring-offset-slate-900 hover:shadow-lg'
                            }`}
                            onClick={() => handleMainImageChange(i)}
                          >
                            <div className="relative overflow-hidden rounded-xl bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 shadow-lg w-16 h-16 sm:w-18 sm:h-18 lg:w-20 lg:h-20">
                              {/* Thumbnail loading state */}
                              {imageLoadingStates[url] && (
                                <div className="absolute inset-0 bg-slate-800/80 backdrop-blur-sm flex items-center justify-center z-10">
                                  <div className="w-4 h-4 border border-cyan-500/30 border-t-cyan-500 rounded-full animate-spin"></div>
                                </div>
                              )}

                              <img
                                src={url}
                                alt={`Thumbnail ${i + 1}`}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                                loading="lazy"
                                onLoad={() => handleImageLoad(url)}
                                onLoadStart={() => handleImageLoadStart(url)}
                              />

                              {/* Hover overlay */}
                              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>



                              {/* Thumbnail number */}
                              <div className="absolute bottom-1 left-1">
                                <div className={`backdrop-blur-sm rounded-md px-1.5 py-0.5 transition-colors duration-300 ${
                                  i === mainImageIndex
                                    ? 'bg-cyan-500/90 text-white'
                                    : 'bg-black/80 text-white'
                                }`}>
                                  <p className="text-xs font-medium leading-none">{i + 1}</p>
                                </div>
                              </div>

                              {/* Active indicator */}
                              {i === mainImageIndex && (
                                <div className="absolute top-1 left-1">
                                  <div className="w-2 h-2 bg-cyan-500 rounded-full animate-pulse"></div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center max-w-lg mx-auto px-4">
                  <div className="relative mb-8 lg:mb-10">
                    <div className="w-24 lg:w-32 h-24 lg:h-32 bg-gradient-to-br from-cyan-500/20 via-blue-500/20 to-purple-500/20 rounded-3xl flex items-center justify-center mx-auto backdrop-blur-sm border border-slate-700/30 shadow-2xl">
                      <ImageIcon className="w-12 lg:w-16 h-12 lg:h-16 text-cyan-400" />
                    </div>
                    <div className="absolute -inset-6 bg-gradient-to-r from-cyan-500/5 via-blue-500/5 to-purple-500/5 rounded-full animate-pulse"></div>
                    <div className="absolute -inset-3 bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-purple-500/10 rounded-full animate-pulse delay-75"></div>
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold mb-4 bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                    Ready to create magic?
                  </h3>
                  <p className="text-slate-400 leading-relaxed text-base lg:text-lg mb-6">
                    Your AI-generated masterpieces will appear here in a beautiful showcase
                  </p>
                  <div className="flex items-center justify-center gap-2 text-slate-500 text-sm">
                    <div className="w-2 h-2 bg-cyan-500/50 rounded-full animate-pulse"></div>
                    <span>Enter a prompt above to get started</span>
                    <div className="w-2 h-2 bg-blue-500/50 rounded-full animate-pulse delay-150"></div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Error Message */}
          {mutation.error && (
            <div className="p-6 mx-8 mb-8">
              <div className="bg-red-900/20 border border-red-800/50 rounded-2xl p-6 backdrop-blur-sm">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-bold">!</span>
                  </div>
                  <div>
                    <h4 className="text-red-300 font-semibold mb-1">Generation Failed</h4>
                    <p className="text-red-400 text-sm">{(mutation.error as Error)?.message}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Prompt Display */}
          {displayedImages && displayedImages.length > 0 && prompt && (
            <div className="border-t border-slate-700/50 bg-slate-800/30 backdrop-blur-sm p-3 lg:p-4">
              <div className="text-center">
                <p className="text-green-400 text-sm font-medium mb-1">Prompt:</p>
                <p className="text-slate-300 text-xs lg:text-sm">{prompt}</p>
              </div>
            </div>
          )}

          {/* Status Bar */}
          <div className="border-t border-slate-700/50 bg-slate-800/30 backdrop-blur-sm p-3 lg:p-4">
            <div className="flex items-center justify-between text-xs lg:text-sm">
              <div className="flex items-center gap-2 lg:gap-4 text-slate-400">
                <span className="hidden sm:inline">API: localhost:3200</span>
                <span className="hidden sm:inline">•</span>
                <span>Model: Gemini 2.0 Flash</span>
              </div>
              <div className="flex items-center gap-2 text-slate-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Ready</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal Popup */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            {/* Close Button */}
            <Button
              variant="secondary"
              size="sm"
              onClick={closeImageModal}
              className="absolute -top-12 right-0 bg-black/80 hover:bg-black/90 text-white border-0 backdrop-blur-sm rounded-xl shadow-lg z-10"
            >
              <X className="w-4 h-4" />
            </Button>

            {/* Modal Image */}
            <div className="relative rounded-2xl overflow-hidden bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 shadow-2xl">
              <img
                src={selectedImage}
                alt={`Generated ${(selectedImageIndex || 0) + 1}`}
                className="w-full h-auto max-h-[80vh] object-contain"
              />

              {/* Mobile Download Button */}
              <div className="lg:hidden absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <Button
                  onClick={() => downloadImage(selectedImage, `ai-generated-${(selectedImageIndex || 0) + 1}.png`)}
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Image
                </Button>
              </div>

              {/* Desktop Action Buttons */}
              <div className="hidden lg:flex absolute top-4 right-4 gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => downloadImage(selectedImage, `ai-generated-${(selectedImageIndex || 0) + 1}.png`)}
                  className="bg-black/80 hover:bg-black/90 text-white border-0 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                  title="Download Image"
                >
                  <Download className="w-4 h-4" />
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => copyImageUrl(selectedImage)}
                  className="bg-black/80 hover:bg-black/90 text-white border-0 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                  title="Copy URL"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>

              {/* Image Info */}
              <div className="absolute bottom-4 left-4">
                <div className="bg-black/80 backdrop-blur-sm rounded-xl px-4 py-2 shadow-lg">
                  <p className="text-white text-sm font-medium">Image {(selectedImageIndex || 0) + 1}</p>
                  <p className="text-slate-300 text-xs">1024×1024 • PNG</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGenerator;
